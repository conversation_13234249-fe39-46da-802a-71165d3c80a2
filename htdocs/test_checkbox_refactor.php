<?php
/**
 * Test script for the refactored Checkbox.php class
 * This script tests the new unified array structure implementation
 */

// Include ImpressCMS bootstrap
define('ICMS_ROOT_PATH', dirname(__FILE__));
require_once ICMS_ROOT_PATH . '/mainfile.php';

// Include the refactored Checkbox class
require_once ICMS_ROOT_PATH . '/libraries/icms/form/elements/Checkbox.php';

echo "<h1>Testing Refactored Checkbox Implementation</h1>\n";

// Test 1: Single checkbox
echo "<h2>Test 1: Single Checkbox</h2>\n";
$checkbox1 = new icms_form_elements_Checkbox('Newsletter', 'newsletter', 1);
$checkbox1->addOption(1, 'Subscribe to newsletter');

echo "<h3>Checkbox Options:</h3>\n";
echo "<pre>" . print_r($checkbox1->getCheckboxOptions(), true) . "</pre>\n";

echo "<h3>Selected Values:</h3>\n";
echo "<pre>" . print_r($checkbox1->getValue(), true) . "</pre>\n";

echo "<h3>Rendered HTML:</h3>\n";
echo $checkbox1->render() . "\n";

// Test 2: Multiple checkboxes
echo "<h2>Test 2: Multiple Checkboxes</h2>\n";
$checkbox2 = new icms_form_elements_Checkbox('Interests', 'interests', array('sports', 'music'));
$checkbox2->addOptionArray(array(
    'sports' => 'Sports',
    'music' => 'Music',
    'movies' => 'Movies',
    'books' => 'Books'
));

echo "<h3>Checkbox Options:</h3>\n";
echo "<pre>" . print_r($checkbox2->getCheckboxOptions(), true) . "</pre>\n";

echo "<h3>Selected Values:</h3>\n";
echo "<pre>" . print_r($checkbox2->getValue(), true) . "</pre>\n";

echo "<h3>Legacy Options (backward compatibility):</h3>\n";
echo "<pre>" . print_r($checkbox2->getOptions(), true) . "</pre>\n";

// Test 3: Changing values after creation
echo "<h2>Test 3: Changing Values After Creation</h2>\n";
$checkbox3 = new icms_form_elements_Checkbox('Colors', 'colors');
$checkbox3->addOptionArray(array(
    'red' => 'Red',
    'green' => 'Green',
    'blue' => 'Blue'
));

echo "<h3>Initial state (no values selected):</h3>\n";
echo "<pre>" . print_r($checkbox3->getCheckboxOptions(), true) . "</pre>\n";

// Set some values
$checkbox3->setValue(array('red', 'blue'));
echo "<h3>After setting values to 'red' and 'blue':</h3>\n";
echo "<pre>" . print_r($checkbox3->getCheckboxOptions(), true) . "</pre>\n";

echo "<h3>Selected Values:</h3>\n";
echo "<pre>" . print_r($checkbox3->getValue(), true) . "</pre>\n";

// Test 4: Backward compatibility
echo "<h2>Test 4: Backward Compatibility</h2>\n";
$checkbox4 = new icms_form_elements_Checkbox('Legacy Test', 'legacy');
$checkbox4->addOption('option1', 'Option 1');
$checkbox4->addOption('option2', 'Option 2');
$checkbox4->setValue('option1');

echo "<h3>Legacy Options Method:</h3>\n";
echo "<pre>" . print_r($checkbox4->getLegacyOptions(), true) . "</pre>\n";

echo "<h3>Legacy Value Method:</h3>\n";
echo "<pre>" . print_r($checkbox4->getLegacyValue(), true) . "</pre>\n";

echo "<h3>New Unified Structure:</h3>\n";
echo "<pre>" . print_r($checkbox4->getCheckboxOptions(), true) . "</pre>\n";

echo "<h2>All Tests Completed!</h2>\n";
?>
