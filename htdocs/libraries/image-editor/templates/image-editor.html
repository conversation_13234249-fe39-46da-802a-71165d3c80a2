<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
	<title><{$smarty.const._IMGMANAGER}></title>
	<link rel="stylesheet" href="<{$icms_url}>/icms<{if $icms_rtl}>_rtl<{/if}>.css">
	<link rel="stylesheet" href="<{$icms_lib_url}>/image-editor/css/xp-info-pane<{if $icms_rtl}>_rtl<{/if}>.css">
	<script type="text/javascript" src="<{$icms_lib_url}>/image-editor/js/xp-info-pane.js"></script>
	<script type="text/javascript" src="<{$icms_lib_url}>/image-editor/js/ajax.js"></script>
	<script type="text/javascript" src="<{$icms_url}>/include/xoops.js"></script>
	<script type="text/javascript">
      var script_server_file = '<{$icms_lib_url}>/image-editor/image-edit.php';
    </script>
</head>
<body onresize="resizePanels();" onload="resizePanels();">
  <div id="pageContent">
    <div id="dhtmlgoodies_xpPane">
	  <div class="dhtmlgoodies_panel">
	    <div>
          <!-- Start content of pane -->
		  <table width="100%">
		    <tr><td align="center" valign="middle"><div class="imanager_image_img"><img src="<{$image.originalurl}>" /></div></td></tr>
		    <tr><td style="overflow: hidden;"><b><{$smarty.const._MD_IMAGECATNAME}></b>: <{$image.title}></td></tr>
		    <tr><td style="overflow: hidden;"><b><{$smarty.const.IMANAGER_FILE}></b>: <{$image.originalname}></td></tr>
		    <tr><td align="center"><b><{$smarty.const.IMANAGER_ORIGINAL}></b></td></tr>
		    <tr><td><b><{$smarty.const._DIMENSION}></b>: <{$image.ori_width}>x<{$image.ori_height}> px</td></tr>
		    <tr><td><b><{$smarty.const.IMANAGER_SIZE}></b>: <{$image.ori_size}></td></tr>
		    <tr><td align="center"><b><{$smarty.const.IMANAGER_EDITED}></b></td></tr>
		    <tr><td><b><{$smarty.const._DIMENSION}></b>: <{$image.width}>x<{$image.height}> px</td></tr>
		    <tr><td><b><{$smarty.const.IMANAGER_SIZE}></b>: <{$image.size}></td></tr>
          </table>
          <!-- End content -->
        </div>		
      </div>
      <{foreach name=loop item=plugin from=$plugins}>
	    <div class="dhtmlgoodies_panel">
	      <div>
          <!-- Start content of pane -->
		  <{$plugin.description}>
		  <{if $plugin.block_template != ''}>
		    <div><{include file="$icms_lib_path/image-editor/plugins/`$plugin.folder`/templates/`$plugin.block_template`" panelID="`$smarty.foreach.loop.iteration`"}></div>
		  <{/if}>  
          <!-- End content -->
          </div>		
        </div>
        <script type="text/javascript">
          exec_function[<{$smarty.foreach.loop.iteration}>] = '<{$plugin.init_js_function}>';
          stop_function[<{$smarty.foreach.loop.iteration}>] = '<{$plugin.stop_js_function}>';
        </script>
      <{/foreach}>
	  <div class="dhtmlgoodies_panel">
	    <div>
          <!-- Start content of pane -->
          <b><{$smarty.const._IMAGEFILTERSSAVE}></b><br />
	      <input type="radio" name="overwrite" value="1" onclick="overpanel(this.value);" checked /><{$smarty.const._YES}>
	      <input type="radio" name="overwrite" value="0" onclick="overpanel(this.value);" /><{$smarty.const._NO}>
		  <div id="overpanel" style="display:none; line-height:20px;">
		    <table width="100%">
		      <tr>
		        <td class="head"><b><{$smarty.const._MD_IMAGECATNAME}>:</b></td>
		        <td class="odd"><input type="text" name="save_img_name" id="save_img_name" size=15 value="<{$smarty.const._MD_IMAGE_COPYOF}><{$image.title}>"></td>
		      </tr>
		      <tr>
		        <td class="head"><b><{$smarty.const._IMGWEIGHT}></b></td>
		        <td class="odd"><input type="text" name="save_img_weight" id="save_img_weight" size="5" value="0"></td>
		      </tr>
		      <tr>
		        <td class="head" colspan="2">
		          <b><{$smarty.const._IMGDISPLAY}></b><br />
		          <input type="radio" name="save_img_display" id="save_img_display" value="1" checked /> <{$smarty.const._YES}> 
		          <input type="radio" name="save_img_display" id="save_img_display" value="0" /> <{$smarty.const._NO}>
		        </td>
		      </tr>
		    </table>
		    </div>
            <div id="cropButtonCell" colspan="2" align="center" style="margin-top:5px;">
              <input type="button" onclick="save_edit()" value="<{$smarty.const._SUBMIT}>">
              <input type="button" onclick="cancel_edit()" value="<{$smarty.const._CANCEL}>" />
                  
              <input type="hidden" id="soverwrite" value="1">
              <input type="hidden" id="save_img_id" value="<{$image.id}>">
              <input type="hidden" id="save_img_tempname" value="<{$image.name}>">
              <input type="hidden" id="save_img_path" value="<{$image.path}>">
              <input type="hidden" id="save_img_url" value="<{$image.url}>">
            </div>
          <!-- End content -->
        </div>		
      </div>
    </div>
    <div class="imageContent" id="contentarea">
      <div><div id="progressBar"></div></div>
      <div id="imageContainer"><img src="<{$image.previewurl}>"></div>
    </div>
  </div>

<script type="text/javascript">
initDhtmlgoodies_xpPane(Array('<{$smarty.const._IMGDETAILS}>'<{foreach item=plugin from=$plugins}>,'<div style="background:url(<{$icms_lib_url}>/image-editor/plugins/<{$plugin.folder}>/<{$plugin.icon}>) no-repeat center left; padding-left: 20px;"><{$plugin.name}></div>'<{/foreach}>,'<{$smarty.const._OPTIONS}>'),Array(true<{foreach item=plugin from=$plugins}>,false<{/foreach}>,true),Array());
</script>

</body>
</html>