![](https://github.com/ImpressCMS/impresscms/blob/HEAD/htdocs/uploads/imagemanager/logos/img482278e29e81c.png?raw=true)

![GitHub Release](https://img.shields.io/github/v/release/impresscms/impresscms?display_name=release&logo=github&style=flat-square)
![GitHub Release](https://img.shields.io/github/v/release/impresscms/impresscms?include_prereleases&display_name=release&logo=github&style=flat-square)

[![FOSSA Status](https://app.fossa.com/api/projects/git%2Bgithub.com%2FImpressCMS%2Fimpresscms.svg?type=shield&issueType=license&style=flat-square)](https://app.fossa.com/projects/git%2Bgithub.com%2FImpressCMS%2Fimpresscms?ref=badge_shield&issueType=license&style=flat-square)
![Code Climate coverage](https://img.shields.io/codeclimate/coverage-letter/ImpressCMS/impresscms?logo=codeclimate&style=flat-square)


![Code Climate maintainability](https://img.shields.io/codeclimate/maintainability/ImpressCMS/impresscms?logo=codeclimate&style=flat-square) <a title="Crowdin" target="_self" href="https://impresscms.crowdin.com/impresscms"><img src="https://badges.crowdin.net/e/f7817e813865fde0509c454ad1ee4c11/localized.svg" /></a>

![Mastodon Follow](https://img.shields.io/mastodon/follow/109613696614549139?domain=https%3A%2F%2Fphpc.social&style=flat-square&logo=mastodon)
<a href="https://www.facebook.com/ImpressCMS/">
<img src="https://img.shields.io/badge/facebook-%3F%3F%3F-%233C5A99.svg?logo=facebook&style=flat-square" alt="FaceBook" /></a>



# ImpressCMS 2.0.2 beta
ImpressCMS is a community developed Content Management System build on PHP and MySQL.

This tool makes maintaining a website as easy as writing a word document.

ImpressCMS is the ideal tool for multi-lingual websites : both the user interface and the content itself can handle multiple languages.

ImpressCMS has these features:
<details>
	<summary>📦 Modules support</summary>
	<blockquote>Website content is managed by separate content modules/web applications. Simply install the module which has the features you need: a news module, forum module, photo album module, there are many many third party modules to choose from.</blockquote>
</details>
<details>
	<summary>🗃️ Database-driven</summary>
	<blockquote>ImpressCMS uses a database to store the data required for running your ImpressCMS site. MySQL and MariaDB is currently supported. Support for other DBMS's is coming soon.</blockquote>
</details>
<details>
	<summary>🌈 Theme-based skinnable interface</summary>
	<blockquote>ImpressCMS is driven by a powerful theme system. Both admins and users can change the look of the entire web site with just a click of a mouse. There are also hundreds of quality themes available for download!</blockquote>
</details>
<details>
	<summary>👥 Versatile Group Permissions System</summary>
	<blockquote>Powerful and user-friendly permissions system which enables administrators to set permissions by group for any registered or all anoymous users.</blockquote>
</details>
<details>
	<summary>🌐 Multi-byte Language Support</summary>
	<blockquote>Fully supports multi-byte languages, including Japanese, Simplified and Traditional Chinese, Korean, etc.</blockquote>
</details>

## What can you do with it?

ImpressCMS can be used for many types of websites. The system is highly scalable and it can be used for example as an intranet for a company with 20,000 employees as well as for building a simple 5-page website for the promotion of your company.
The system is extremely useful for managing online communities because it has the ability to create user groups and assign permissions for managing content to each different group.

For each type of website ImpressCMS offers different functionality with a collection of free modules that are available on [https://www.impresscms.org/modules/downloads/](https://www.impresscms.org/modules/downloads/). A few examples of what you can do with them:

* Publish news of your organization
* Let visitors contact you through a state of the art customizable contact form
* Create and manage articles
* Add a forum to your site
* Sell products through your website using an online store
* ...and many more


## Installation

To install and use this package, transfer this package to your web server, putting the contents of the htdocs/ folder in your web root, and follow the steps of the installer.

More detailed installation instructions are available [on the ImpressCMS site](https://www.impresscms.org/modules/simplywiki/index.php?page=Installation)

## Do you need any help?

You can find our official documentation at [our wiki](https://www.impresscms.org/modules/simplywiki/).

Also, help could be found on [our official forums](https://www.impresscms.org/modules/iforum/).

## How to contribute?
### Code contribution
Open an [issue](https://github.com/ImpressCMS/impresscms/issues/new) or send us a [pull request](https://github.com/ImpressCMS/impresscms/pulls). If you plan on working on more than just a simple bugfix, discuss your plans first on the forums.

All active development branches are named in format `MAJOR.MINOR.x`. So if you want to fix or add something new, you should branch from most similar looking branch.

E.g.: if you want to do with latest **2.0** code, you need to branch from `2.0.x` branch.

If you not sure how can be possible to work with Git or/and GitHub, try [interactive GitHub tutorial](https://skills.github.com).
### Translation
ImpressCMS is built to work with multiple languages, for the user interface of the administration interface to the content managed on the site.

We use Crowdin to manage our translations, please check out [https://impresscms.crowdin.com](https://impresscms.crowdin.com/u/projects/1)
## License
<a href="https://app.fossa.io/projects/git%2Bgithub.com%2FImpressCMS%2Fimpresscms?ref=badge_large"><img src="https://app.fossa.io/api/projects/git%2Bgithub.com%2FImpressCMS%2Fimpresscms.svg?type=large" alt="FOSSA Status" align="right" /></a>
ImpressCMS is licensed right now in <a href="https://github.com/ImpressCMS/impresscms/blob/HEAD/LICENSE">GPL 2.0 license</a> but is depended on libraries that uses other compatible open source licenses.

Quick summary what GPL 2.0 really means can be found at [TLDRLegal website](https://tldrlegal.com/license/gnu-general-public-license-v2).

We use FOSSA to detect if there are any incompatibilities and if there is any solve them. The graphic on the right is used to iliustrate what licenses are used when project is installed.
