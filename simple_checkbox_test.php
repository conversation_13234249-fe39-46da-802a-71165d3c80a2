<?php
/**
 * Simple test for the refactored Checkbox class without full ImpressCMS environment
 */

// Define ICMS_ROOT_PATH to avoid the check in Checkbox.php
define('ICMS_ROOT_PATH', dirname(__FILE__) . '/htdocs');

// Mock the base class for testing
class icms_form_Element {
    protected $caption;
    protected $name;
    protected $extra = '';
    protected $customTemplate = null;

    public function setCaption($caption) { $this->caption = $caption; }
    public function setName($name) { $this->name = $name; }
    public function getName() { return $this->name; }
    public function getExtra() { return $this->extra; }
}

// Mock template class
class icms_view_Tpl {
    private $vars = array();

    public function assign($key, $value) {
        $this->vars[$key] = $value;
    }

    public function fetch($template) {
        return "Template: $template with vars: " . print_r($this->vars, true);
    }
}

// Include the refactored Checkbox class
require_once 'htdocs/libraries/icms/form/elements/Checkbox.php';

echo "<h1>Simple Checkbox Test</h1>\n";

// Test 1: Basic functionality
echo "<h2>Test 1: Basic Functionality</h2>\n";
$checkbox = new icms_form_elements_Checkbox('Test', 'test');
$checkbox->addOption('option1', 'Option 1');
$checkbox->addOption('option2', 'Option 2');
$checkbox->setValue('option1');

echo "<h3>Unified Options:</h3>\n";
echo "<pre>" . print_r($checkbox->getCheckboxOptions(), true) . "</pre>\n";

echo "<h3>Selected Values:</h3>\n";
echo "<pre>" . print_r($checkbox->getValue(), true) . "</pre>\n";

// Test 2: Multiple selections
echo "<h2>Test 2: Multiple Selections</h2>\n";
$checkbox2 = new icms_form_elements_Checkbox('Multi', 'multi');
$checkbox2->addOptionArray(array(
    'a' => 'Option A',
    'b' => 'Option B',
    'c' => 'Option C'
));
$checkbox2->setValue(array('a', 'c'));

echo "<h3>Unified Options:</h3>\n";
echo "<pre>" . print_r($checkbox2->getCheckboxOptions(), true) . "</pre>\n";

echo "<h3>Selected Values:</h3>\n";
echo "<pre>" . print_r($checkbox2->getValue(), true) . "</pre>\n";

// Test 3: Backward compatibility
echo "<h2>Test 3: Backward Compatibility</h2>\n";
echo "<h3>Legacy Options:</h3>\n";
echo "<pre>" . print_r($checkbox2->getOptions(), true) . "</pre>\n";

echo "<h3>Legacy Value:</h3>\n";
echo "<pre>" . print_r($checkbox2->getLegacyValue(), true) . "</pre>\n";

echo "<h2>All Tests Completed Successfully!</h2>\n";
?>
