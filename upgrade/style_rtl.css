/**
* ImpressCMS styles
*
* upgrader style sheet information are rendered from here.
*
* @copyright	The ImpressCMS Project http://www.impresscms.org/
* @license	http://www.gnu.org/licenses/old-licenses/gpl-2.0.html GNU General Public License (GPL)
* @package	upgrader
* @since	1.1
* <AUTHOR> (aka stranger) <<EMAIL>>
* @version	$Id$
*/
body {font-family : Tahoma, verdana, arial, helvetica, sans-serif;}

table {direction : rtl; text-align : right;}

table.toptable , table.maintable , table.footertable{direction : rtl;}

table td {font-family : Tahoma, verdana, arial, helvetica, sans-serif;}

body td , p , div {direction : rtl; font-family : Tahoma, verdana, arial, helvetica, sans-serif;}

a:link , a:active , a:visited {direction : rtl; font-family : Tahoma, verdana, arial, helvetica, sans-serif;} 

a:hover {direction : rtl; font-family : Tahoma, verdana, arial, helvetica, sans-serif;} 

h1 {direction : rtl; font-family : Tahoma, verdana, arial, helvetica, sans-serif; text-align: Justify;}

h2 {direction : rtl; font-family : Tahoma, verdana, arial, helvetica, sans-serif; text-align: Justify;}

h4 {direction : rtl; font-family : Tahoma, verdana, arial, helvetica, sans-serif; text-align: Justify;}  

ul {text-align : right; font-family : Tahoma, verdana, arial, helvetica, sans-serif; direction : rtl;} 

input , select , textarea {direction : rtl; font-family : Tahoma, verdana, arial, helvetica, sans-serif; text-align : right;}

html, body {
	margin:		0px;
	padding:	0px;
	height:		100%;
}

body {
	font:		10pt Lucida Grande, tahoma, osaka,taipei,verdana, arial, helvetica, sans-serif;
	color:		#000000;
	background:	#c6c7c9;
}

#xo-banner {
	background:		url(img/top_bg.png) repeat-x left top;
	height:			110px;
}
#xo-content {
	margin:					1em 2em;
	padding:				.5em;
	background:				#fff;
	border:					1px solid #666;
	border-right-width:		3px;
	border-bottom-width:	2px;
	min-height:				400px;
}
#check_results {
	width:					30em;
}


a		{
    text-decoration: none;
	color: #666666;font-weight: bold;
	background-color: transparent;
}

a:hover		{
    text-decoration: none;
    	color: #ff9966;
    	font-weight: bold;
    	background-color: transparent;
}

h1 {
	clear:			both;
	margin-top:		.25em;
	margin-bottom:	.5em;
	border-bottom:	1px solid #ccc;
	padding-left:	0;
	font-size:		200%;
	font-weight:	bold;
	color:			#666F7D;
}


img		{	border:0;			}

label {
	display:		block;
	padding:		0px;
}
.xo-formfield {
	margin-bottom:	.2em;
}
.xo-formbuttons {
	padding-top:	.5em;
	padding-left:	0px;
}

.xo-formfield.required label:after {
	content:		'*';
	color:			#ff0000;
}

input[type=text], input[type=password], textarea {
	border:				1px solid #ddd;
	margin:				2px 0px;
	padding-left:		.2em;
	background-color:	#fff;
    MIN-WIDTH: 60%;
}

.x2-note {
	margin:				1em .5em;
	padding:			.5em;
	background-color:	#F4FAFF;
}

#link-next {
	display:		block;
	text-align:		right;
	margin-top:		2em;

}
li {
	margin-left: 30px;
}
#help_button
{
    FLOAT: right;
    CURSOR: pointer;
}
.xoform-help
{
    DISPLAY: none;
	background-color:	#F4FAFF;
	border:				1px solid #E4EAEF;
    PADDING:0px;
    MARGIN: .5em;
}
BODY.show-help .xoform-help
{
    DISPLAY: block
}